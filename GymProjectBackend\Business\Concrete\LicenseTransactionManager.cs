﻿using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using System;
using System.Collections.Generic;

namespace Business.Concrete
{
    public class LicenseTransactionManager : ILicenseTransactionService
    {
        private readonly ILicenseTransactionDal _licenseTransactionDal;

        public LicenseTransactionManager(ILicenseTransactionDal licenseTransactionDal)
        {
            _licenseTransactionDal = licenseTransactionDal;
        }
        [SecuredOperation("owner")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspect("LicenseTransaction")]
        public IResult Add(LicenseTransaction licenseTransaction)
        {
            licenseTransaction.CreationDate = DateTime.Now;
            licenseTransaction.IsActive = true;
            _licenseTransactionDal.Add(licenseTransaction);
            return new SuccessResult("Lisans işlemi başarıyla eklendi");
        }

        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 60, "LicenseTransaction", "Business")]
        public IDataResult<List<LicenseTransaction>> GetAll()
        {
            var transactions = _licenseTransactionDal.GetAll(lt => lt.IsActive)
                .OrderByDescending(lt => lt.TransactionDate)
                .ToList();
            return new SuccessDataResult<List<LicenseTransaction>>(transactions);
        }

        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        public IDataResult<List<LicenseTransaction>> GetAllFiltered(int? userID, string startDate, string endDate, int page, int pageSize)
        {
            return _licenseTransactionDal.GetAllFiltered(userID, startDate, endDate, page, pageSize);
        }

        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 60, "LicenseTransaction", "User")]
        public IDataResult<List<LicenseTransaction>> GetByUserId(int userId)
        {
            return new SuccessDataResult<List<LicenseTransaction>>(
                _licenseTransactionDal.GetAll(lt => lt.UserID == userId && lt.IsActive));
        }

        [SecuredOperation("owner")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspect("LicenseTransaction")]
        public IResult Delete(int id)
        {
            var transaction = _licenseTransactionDal.Get(lt => lt.LicenseTransactionID == id);
            if (transaction == null)
            {
                return new ErrorResult("Lisans işlemi bulunamadı");
            }

            transaction.IsActive = false;
            _licenseTransactionDal.Update(transaction);
            return new SuccessResult("Lisans işlemi başarıyla silindi");
        }

        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 30, "LicenseTransaction", "Totals")]
        public IDataResult<object> GetTotals()
        {
            var transactions = _licenseTransactionDal.GetAll(lt => lt.IsActive);

            var totals = new
            {
                TotalAmount = transactions.Sum(t => t.Amount),
                TotalCash = transactions.Where(t => t.PaymentMethod.Contains("Nakit")).Sum(t => t.Amount),
                TotalCreditCard = transactions.Where(t => t.PaymentMethod.Contains("Kredi Kartı")).Sum(t => t.Amount),
                TotalTransfer = transactions.Where(t => t.PaymentMethod.Contains("Havale")).Sum(t => t.Amount)
            };

            return new SuccessDataResult<object>(totals);
        }

        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 60, "LicenseTransaction", "MonthlyRevenue")]
        public IDataResult<object> GetMonthlyRevenue(int year)
        {
            var transactions = _licenseTransactionDal.GetAll(lt => lt.IsActive && lt.TransactionDate.Year == year);

            var monthlyData = new List<decimal>();
            for (int month = 1; month <= 12; month++)
            {
                var monthlyTotal = transactions
                    .Where(t => t.TransactionDate.Month == month)
                    .Sum(t => t.Amount);
                monthlyData.Add(monthlyTotal);
            }

            var result = new
            {
                Year = year,
                MonthlyRevenues = monthlyData
            };

            return new SuccessDataResult<object>(result);
        }
    }
}