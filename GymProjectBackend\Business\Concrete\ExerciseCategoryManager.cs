using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Transaction;
using Core.Aspects.Autofac.Validation;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Concrete
{
    public class ExerciseCategoryManager : IExerciseCategoryService
    {
        IExerciseCategoryDal _exerciseCategoryDal;

        public ExerciseCategoryManager(IExerciseCategoryDal exerciseCategoryDal)
        {
            _exerciseCategoryDal = exerciseCategoryDal;
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<List<ExerciseCategoryDto>> GetAllCategories()
        {
            var result = _exerciseCategoryDal.GetAllCategories();
            return new SuccessDataResult<List<ExerciseCategoryDto>>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<List<ExerciseCategoryDto>> GetActiveCategories()
        {
            var result = _exerciseCategoryDal.GetActiveCategories();
            return new SuccessDataResult<List<ExerciseCategoryDto>>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<ExerciseCategoryDto> GetById(int categoryId)
        {
            var result = _exerciseCategoryDal.GetCategoryById(categoryId);
            if (result == null)
            {
                return new ErrorDataResult<ExerciseCategoryDto>("Egzersiz kategorisi bulunamadı.");
            }

            return new SuccessDataResult<ExerciseCategoryDto>(result);
        }

        [SecuredOperation("owner")] // Sadece owner kategorileri ekleyebilir
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        public IResult Add(ExerciseCategoryAddDto categoryAddDto)
        {
            var category = new ExerciseCategory
            {
                CategoryName = categoryAddDto.CategoryName,
                Description = categoryAddDto.Description,
                IsActive = true,
                CreationDate = DateTime.Now
            };

            _exerciseCategoryDal.Add(category);
            return new SuccessResult("Egzersiz kategorisi başarıyla eklendi.");
        }

        [SecuredOperation("owner")] // Sadece owner kategorileri güncelleyebilir
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        public IResult Update(ExerciseCategoryUpdateDto categoryUpdateDto)
        {
            var existingCategory = _exerciseCategoryDal.Get(c => c.ExerciseCategoryID == categoryUpdateDto.ExerciseCategoryID);
            if (existingCategory == null)
            {
                return new ErrorResult("Egzersiz kategorisi bulunamadı.");
            }

            existingCategory.CategoryName = categoryUpdateDto.CategoryName;
            existingCategory.Description = categoryUpdateDto.Description;
            existingCategory.IsActive = categoryUpdateDto.IsActive;
            existingCategory.UpdatedDate = DateTime.Now;

            _exerciseCategoryDal.Update(existingCategory);
            return new SuccessResult("Egzersiz kategorisi başarıyla güncellendi.");
        }

        [SecuredOperation("owner")] // Sadece owner kategorileri silebilir
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        public IResult Delete(int categoryId)
        {
            var category = _exerciseCategoryDal.Get(c => c.ExerciseCategoryID == categoryId);
            if (category == null)
            {
                return new ErrorResult("Egzersiz kategorisi bulunamadı.");
            }

            // Soft delete
            category.IsActive = false;
            category.DeletedDate = DateTime.Now;

            _exerciseCategoryDal.Update(category);
            return new SuccessResult("Egzersiz kategorisi başarıyla silindi.");
        }
    }
}
