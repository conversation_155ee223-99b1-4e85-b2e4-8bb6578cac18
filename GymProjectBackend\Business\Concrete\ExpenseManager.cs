using Business.Abstract;
using Business.Constants;
using Business.ValidationRules.FluentValidation;
using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Validation;
using Core.Aspects.Autofac.Transaction;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Utilities.Results;
using Core.Utilities.Paging;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using Core.Utilities.Business;
using Business.BusinessAscpects.Autofac;
using Core.Utilities.Security.CompanyContext;

namespace Business.Concrete
{
    public class ExpenseManager : IExpenseService
    {
        private readonly IExpenseDal _expenseDal;
        private readonly ICompanyContext _companyContext;

        public ExpenseManager(IExpenseDal expenseDal, ICompanyContext companyContext)
        {
            _expenseDal = expenseDal;
            _companyContext = companyContext;
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [ValidationAspect(typeof(ExpenseValidator))] // Validator eklendi
        [SmartCacheRemoveAspect("Expense")]
        [TransactionScopeAspect]
        public IResult Add(Expense expense)
        {
            // Şirket ID'sini bağlamdan alıp entity'ye ata
            expense.CompanyID = _companyContext.GetCompanyId();

            // İş kuralları eklenebilir (örn: Amount negatif olamaz vb.)
            IResult ruleResult = BusinessRules.Run(CheckIfAmountIsPositive(expense.Amount));
            if (ruleResult != null)
            {
                return ruleResult;
            }

            _expenseDal.Add(expense);
            return new SuccessResult(Messages.ExpenseAdded);
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [SmartCacheRemoveAspect("Expense")]
        [TransactionScopeAspect]
        public IResult Delete(int expenseId)
        {
            // Önce giderin mevcut şirkete ait olup olmadığını kontrol et
            var expenseToDelete = _expenseDal.Get(e => e.ExpenseID == expenseId && e.CompanyID == _companyContext.GetCompanyId());
            if (expenseToDelete == null)
            {
                return new ErrorResult(Messages.ExpenseNotFound);
            }

            _expenseDal.Delete(expenseId); // EfBaseRepository ID ile siliyor
            return new SuccessResult(Messages.ExpenseDeleted);
        }

        [SecuredOperation("owner,admin")]
        [MultiTenantCacheAspect(duration: 60, "Expense", "Business")]
        public IDataResult<List<Expense>> GetAll()
        {
            var companyId = _companyContext.GetCompanyId();
            return new SuccessDataResult<List<Expense>>(_expenseDal.GetAll(e => e.CompanyID == companyId && e.IsActive));
        }

        [SecuredOperation("owner,admin")]
        [MultiTenantCacheAspect(duration: 60, "Expense", "Details")]
        public IDataResult<Expense> GetById(int expenseId)
        {
            var companyId = _companyContext.GetCompanyId();
            var expense = _expenseDal.Get(e => e.ExpenseID == expenseId && e.CompanyID == companyId && e.IsActive);
            if (expense == null)
            {
                return new ErrorDataResult<Expense>(Messages.ExpenseNotFound);
            }
            return new SuccessDataResult<Expense>(expense);
        }

        [SecuredOperation("owner,admin")]
        [MultiTenantCacheAspect(duration: 60, "Expense", "DateRange")]
        public IDataResult<List<ExpenseDto>> GetExpensesByDateRange(DateTime startDate, DateTime endDate)
        {
            var result = _expenseDal.GetExpensesByDateRange(startDate, endDate);
            return new SuccessDataResult<List<ExpenseDto>>(result);
        }




        [SecuredOperation("owner,admin")]
        [LogAspect]
        [ValidationAspect(typeof(ExpenseValidator))] // Validator eklendi
        [SmartCacheRemoveAspect("Expense")]
        [TransactionScopeAspect]
        public IResult Update(Expense expense)
        {
            // Güncellenecek giderin mevcut şirkete ait olup olmadığını kontrol et
             var existingExpense = _expenseDal.Get(e => e.ExpenseID == expense.ExpenseID && e.CompanyID == _companyContext.GetCompanyId());
            if (existingExpense == null)
            {
                return new ErrorResult(Messages.ExpenseNotFound);
            }

            // Şirket ID'sini tekrar ata (güvenlik için)
            expense.CompanyID = _companyContext.GetCompanyId();
            // CreationDate'in güncellenmemesini sağla (EfBaseRepository hallediyor ama garanti olsun)
            expense.CreationDate = existingExpense.CreationDate;

            // İş kuralları
            IResult ruleResult = BusinessRules.Run(CheckIfAmountIsPositive(expense.Amount));
            if (ruleResult != null)
            {
                return ruleResult;
            }

            _expenseDal.Update(expense);
            return new SuccessResult(Messages.ExpenseUpdated);
        }

        // --- İş Kuralları ---
        private IResult CheckIfAmountIsPositive(decimal amount)
        {
            if (amount <= 0)
            {
                return new ErrorResult(Messages.AmountMustBePositive);
            }
            return new SuccessResult();
        }

        [SecuredOperation("owner,admin")]
        [MultiTenantCacheAspect(duration: 30, "Expense", "Dashboard")]
        public IDataResult<ExpenseDashboardDto> GetExpenseDashboardData(int year, int month)
        {
            var result = _expenseDal.GetExpenseDashboardData(year, month);
            return new SuccessDataResult<ExpenseDashboardDto>(result, "Dashboard verileri başarıyla getirildi.");
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<PaginatedResult<ExpenseDto>> GetExpensesPaginated(ExpensePagingParameters parameters)
        {
            var result = _expenseDal.GetExpensesPaginated(parameters);
            return new SuccessDataResult<PaginatedResult<ExpenseDto>>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<List<ExpenseDto>> GetAllExpensesFiltered(ExpensePagingParameters parameters)
        {
            var result = _expenseDal.GetAllExpensesFiltered(parameters);
            return new SuccessDataResult<List<ExpenseDto>>(result);
        }
    }
}